<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动关机设置</title>
    <style>
        :root {
            --bg-color: #fff;
            --text-color: #111;
            --text-secondary: #666;
            --border-color: #ddd;
            --accent-color: #2563eb;
            --success-color: #16a34a;
            --danger-color: #dc2626;
            --radius: 6px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
            background: #f5f5f5;
            color: var(--text-color);
            line-height: 1.4;
        }

        .header-bg {
            padding: 8px;
            text-align: center;
            background: var(--bg-color);
            border-bottom: 1px solid var(--border-color);
        }

        .header-bg h1 {
            font-size: 1.125rem;
            font-weight: 600;
        }

        .container {
            max-width: 720px;
            margin: 0 auto;
            padding: 8px;
        }

        .card {
            background: var(--bg-color);
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            padding: 8px;
            margin-bottom: 8px;
        }

        .card-header {
            display: flex;
            gap: 6px;
            align-items: center;
            margin-bottom: 6px;
        }

        .card-header h2 {
            font-size: 1rem;
            font-weight: 600;
            flex-grow: 1;
        }

        .schedules-list {
            display: grid;
            gap: 3px;
        }

        .schedule-item {
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            padding: 6px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .schedule-item:hover {
            background: #f8fafc;
        }

        .schedule-info {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .schedule-info h3 {
            font-size: 0.875rem;
            font-weight: 500;
        }

        .schedule-info p {
            color: var(--text-secondary);
            font-size: 0.75rem;
        }

        .schedule-status {
            padding: 2px 6px;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .schedule-status.enabled {
            background: #e6f4ea;
            color: var(--success-color);
        }

        .schedule-status.disabled {
            background: #f0f0f0;
            color: var(--text-secondary);
        }

        .schedule-actions {
            display: flex;
            gap: 4px;
        }

        .no-schedules {
            text-align: center;
            color: var(--text-secondary);
            padding: 12px;
            border-radius: var(--radius);
            border: 1px dashed var(--border-color);
            font-size: 0.75rem;
        }

        .settings-toggle {
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            padding: 6px;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            margin-top: 6px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .settings-toggle:hover {
            background: #f8fafc;
        }

        .settings-content {
            display: none;
            padding: 6px;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            margin-top: 3px;
        }

        .settings-content.active {
            display: block;
        }

        .settings-list {
            display: grid;
            gap: 3px;
        }

        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
        }

        .setting-item .label-text {
            font-size: 0.75rem;
            font-weight: 500;
        }

        .setting-item input[type="number"] {
            padding: 3px 6px;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            font-size: 0.75rem;
            width: 50px;
        }

        .setting-item input[type="number"]:focus {
            outline: none;
            border-color: var(--accent-color);
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 28px;
            height: 14px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: #ccc;
            border-radius: 9999px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 10px;
            width: 10px;
            left: 2px;
            bottom: 2px;
            background: #fff;
            border-radius: 50%;
            transition: transform 0.2s;
        }

        input:checked + .slider {
            background: var(--success-color);
        }

        input:checked + .slider:before {
            transform: translateX(14px);
        }

        .btn {
            padding: 4px 8px;
            border-radius: var(--radius);
            font-size: 0.75rem;
            font-weight: 500;
            cursor: pointer;
            text-align: center;
            color: #fff;
            border: none;
        }

        .btn-primary {
            background: var(--accent-color);
        }

        .btn-primary:hover {
            background: #1e40af;
        }

        .btn-success {
            background: var(--success-color);
        }

        .btn-success:hover {
            background: #15803d;
        }

        .btn-danger {
            background: var(--danger-color);
        }

        .btn-danger:hover {
            background: #b91c1c;
        }

        .btn-small {
            padding: 3px 6px;
            font-size: 0.75rem;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.4);
        }

        .modal-content {
            background: var(--bg-color);
            margin: 15% auto;
            border-radius: var(--radius);
            width: 90%;
            max-width: 320px;
        }

        .modal-header {
            padding: 6px 10px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            font-size: 0.875rem;
            font-weight: 600;
        }

        .close {
            color: var(--text-secondary);
            font-size: 14px;
            cursor: pointer;
        }

        .close:hover {
            color: var(--text-color);
        }

        .modal-body {
            padding: 10px;
        }

        .modal-footer {
            padding: 6px 10px;
            border-top: 1px solid var(--border-color);
            display: flex;
            gap: 4px;
            justify-content: flex-end;
            background: #f5f5f5;
            border-bottom-left-radius: var(--radius);
            border-bottom-right-radius: var(--radius);
        }

        .form-group {
            margin-bottom: 6px;
        }

        .form-group label {
            display: block;
            margin-bottom: 2px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .form-group input[type="text"] {
            width: 100%;
            padding: 3px 6px;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            font-size: 0.75rem;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--accent-color);
        }

        .time-picker {
            display: flex;
            gap: 4px;
            align-items: center;
        }

        .time-picker input[type="number"] {
            width: 40px;
            padding: 3px 6px;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            font-size: 0.75rem;
            text-align: center;
        }

        .time-picker input[type="number"]:focus {
            outline: none;
            border-color: var(--accent-color);
        }

        .days-selector {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 4px;
            margin-top: 4px;
        }

        .day-checkbox {
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            padding: 4px;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            font-size: 0.75rem;
        }

        .day-checkbox:hover {
            background: #f8fafc;
        }

        .day-checkbox input[type="checkbox"] {
            display: none;
        }

        .day-checkbox:has(input:checked) {
            background: var(--accent-color);
            border-color: var(--accent-color);
            color: #fff;
        }

        @media (max-width: 768px) {
            .container {
                padding: 4px;
            }

            .card {
                padding: 6px;
            }

            .schedule-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 4px;
            }

            .schedule-actions {
                width: 100%;
                justify-content: flex-end;
            }

            .days-selector {
                grid-template-columns: repeat(4, 1fr);
            }

            .card-header {
                flex-wrap: wrap;
                gap: 4px;
            }

            .btn {
                width: 100%;
            }
        }

        @media (max-width: 480px) {
            .header-bg h1 {
                font-size: 1rem;
            }

            .card-header h2 {
                font-size: 0.875rem;
            }

            .days-selector {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="header-bg">
        <h1>自动关机设置</h1>
    </div>

    <div class="container">
        <div class="card">
            <div class="card-header">
                <h2>关机计划</h2>
                <button id="add-schedule-btn" class="btn btn-primary">添加计划</button>
                <button id="save-btn" class="btn btn-success">保存设置</button>
            </div>
            <div id="schedules-list" class="schedules-list"></div>
            <div class="settings-toggle" onclick="this.nextElementSibling.classList.toggle('active')">
                <span>通用设置</span>
                <span>▼</span>
            </div>
            <div class="settings-content">
                <div class="settings-list">
                    <div class="setting-item">
                        <span class="label-text">关机前倒计时 (秒):</span>
                        <input type="number" id="countdown-duration" min="10" max="300" value="30">
                    </div>
                    <div class="setting-item">
                        <span class="label-text">启用通知</span>
                        <label class="toggle-switch">
                            <input type="checkbox" id="enable-notifications" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="schedule-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">添加计划</h3>
                <span class="close">×</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="schedule-name">计划名称:</label>
                    <input type="text" id="schedule-name" placeholder="例如：工作日晚上">
                </div>
                <div class="form-group">
                    <label>时间:</label>
                    <div class="time-picker">
                        <input type="number" id="schedule-hour" min="0" max="23" value="22" placeholder="时">
                        <span>:</span>
                        <input type="number" id="schedule-minute" min="0" max="59" value="0" placeholder="分">
                    </div>
                </div>
                <div class="form-group">
                    <label>星期:</label>
                    <div class="days-selector">
                        <label class="day-checkbox">
                            <input type="checkbox" value="monday"><span>一</span>
                        </label>
                        <label class="day-checkbox">
                            <input type="checkbox" value="tuesday"><span>二</span>
                        </label>
                        <label class="day-checkbox">
                            <input type="checkbox" value="wednesday"><span>三</span>
                        </label>
                        <label class="day-checkbox">
                            <input type="checkbox" value="thursday"><span>四</span>
                        </label>
                        <label class="day-checkbox">
                            <input type="checkbox" value="friday"><span>五</span>
                        </label>
                        <label class="day-checkbox">
                            <input type="checkbox" value="saturday"><span>六</span>
                        </label>
                        <label class="day-checkbox">
                            <input type="checkbox" value="sunday"><span>日</span>
                        </label>
                    </div>
                </div>
                <div class="form-group">
                    <div class="setting-item" style="padding: 0; background: none; border: none;">
                        <span class="label-text">启用此计划</span>
                        <label class="toggle-switch">
                            <input type="checkbox" id="schedule-enabled" checked>
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button id="cancel-schedule-btn" class="btn btn-primary">取消</button>
                <button id="save-schedule-btn" class="btn btn-primary">保存</button>
            </div>
        </div>
    </div>

    <script>
        // In-memory schedule storage
        let schedules = [];
        let editingIndex = null;

        // DOM elements
        const modal = document.getElementById('schedule-modal');
        const schedulesList = document.getElementById('schedules-list');
        const addScheduleBtn = document.getElementById('add-schedule-btn');
        const saveScheduleBtn = document.getElementById('save-schedule-btn');
        const cancelScheduleBtn = document.getElementById('cancel-schedule-btn');
        const closeModal = document.querySelector('.close');
        const modalTitle = document.getElementById('modal-title');
        const scheduleNameInput = document.getElementById('schedule-name');
        const scheduleHourInput = document.getElementById('schedule-hour');
        const scheduleMinuteInput = document.getElementById('schedule-minute');
        const scheduleEnabledCheckbox = document.getElementById('schedule-enabled');
        const saveSettingsBtn = document.getElementById('save-btn');
        const countdownDurationInput = document.getElementById('countdown-duration');
        const enableNotificationsCheckbox = document.getElementById('enable-notifications');

        // Open modal for adding a new schedule
        addScheduleBtn.addEventListener('click', () => {
            editingIndex = null;
            modalTitle.textContent = '添加计划';
            scheduleNameInput.value = '';
            scheduleHourInput.value = '22';
            scheduleMinuteInput.value = '0';
            scheduleEnabledCheckbox.checked = true;
            document.querySelectorAll('.day-checkbox input').forEach(checkbox => checkbox.checked = false);
            modal.style.display = 'block';
        });

        // Close modal
        const closeModalFn = () => {
            modal.style.display = 'none';
        };

        closeModal.addEventListener('click', closeModalFn);
        cancelScheduleBtn.addEventListener('click', closeModalFn);

        // Save schedule
        saveScheduleBtn.addEventListener('click', () => {
            const name = scheduleNameInput.value.trim();
            const hour = parseInt(scheduleHourInput.value, 10);
            const minute = parseInt(scheduleMinuteInput.value, 10);
            const days = Array.from(document.querySelectorAll('.day-checkbox input:checked')).map(input => input.value);
            const enabled = scheduleEnabledCheckbox.checked;

            if (!name || days.length === 0) {
                alert('请填写计划名称并选择至少一天');
                return;
            }

            if (isNaN(hour) || hour < 0 || hour > 23 || isNaN(minute) || minute < 0 || minute > 59) {
                alert('请输入有效时间（小时：0-23，分钟：0-59）');
                return;
            }

            const time = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
            const schedule = { name, time, days, enabled };

            if (editingIndex !== null) {
                schedules[editingIndex] = schedule;
            } else {
                schedules.push(schedule);
            }

            renderSchedules();
            closeModalFn();
        });

        // Save general settings
        saveSettingsBtn.addEventListener('click', () => {
            const countdown = parseInt(countdownDurationInput.value, 10);
            const notifications = enableNotificationsCheckbox.checked;
            if (countdown < 10 || countdown > 300) {
                alert('倒计时必须在10到300秒之间');
                return;
            }
            alert(`设置已保存: 倒计时=${countdown}秒, 通知=${notifications ? '启用' : '禁用'}`);
        });

        // Render schedules
        function renderSchedules() {
            schedulesList.innerHTML = schedules.length === 0
                ? '<div class="no-schedules">暂无计划</div>'
                : schedules.map((schedule, index) => `
                    <div class="schedule-item">
                        <div class="schedule-info">
                            <span class="schedule-status ${schedule.enabled ? 'enabled' : 'disabled'}">
                                ${schedule.enabled ? '已启用' : '已禁用'}
                            </span>
                            <div>
                                <h3>${schedule.name}</h3>
                                <p>时间: ${schedule.time}</p>
                                <p>星期: ${schedule.days.map(day => day.slice(0, 1).toUpperCase()).join(', ')}</p>
                            </div>
                        </div>
                        <div class="schedule-actions">
                            <button class="btn btn-small btn-primary edit-btn" data-index="${index}">编辑</button>
                            <button class="btn btn-small btn-danger delete-btn" data-index="${index}">删除</button>
                        </div>
                    </div>
                `).join('');
        }

        // Handle edit and delete buttons
        schedulesList.addEventListener('click', (e) => {
            if (e.target.classList.contains('edit-btn')) {
                const index = parseInt(e.target.dataset.index, 10);
                editingIndex = index;
                const schedule = schedules[index];
                modalTitle.textContent = '编辑计划';
                scheduleNameInput.value = schedule.name;
                const [hour, minute] = schedule.time.split(':').map(num => parseInt(num, 10));
                scheduleHourInput.value = hour;
                scheduleMinuteInput.value = minute;
                scheduleEnabledCheckbox.checked = schedule.enabled;
                document.querySelectorAll('.day-checkbox input').forEach(checkbox => {
                    checkbox.checked = schedule.days.includes(checkbox.value);
                });
                modal.style.display = 'block';
            } else if (e.target.classList.contains('delete-btn')) {
                const index = parseInt(e.target.dataset.index, 10);
                if (confirm('确定删除此计划？')) {
                    schedules.splice(index, 1);
                    renderSchedules();
                }
            }
        });

        // Initial render
        renderSchedules();
    </script>
</body>
</html>